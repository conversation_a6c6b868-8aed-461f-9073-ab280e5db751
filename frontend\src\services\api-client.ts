import { useAuth as useClerkAuth } from "@clerk/clerk-react";
import { env } from "../config/env";

// Base API types
export interface ApiResponse<T> {
  data: T;
  meta?: Record<string, unknown>;
  timestamp?: string;
}

export interface ApiError {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// User-related types
export interface User {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  userRoles?: Array<{
    id: string;
    role: {
      id: string;
      name: string;
      type: string;
      description: string;
    };
  }>;
  // Tenant information (included for System Admins)
  tenant?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface UsersResponse {
  data: User[];
  meta: {
    count: number;
    tenantId?: string;
  };
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  isActive?: boolean;
}

// Tenant-related types
export interface Tenant {
  id: string;
  name: string;
  slug: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TenantsResponse {
  data: Tenant[];
  meta: {
    count: number;
    scope: "all" | "tenant";
  };
}

export interface CreateTenantData {
  name: string;
  slug: string;
}

export interface UpdateTenantData {
  name?: string;
  slug?: string;
  isActive?: boolean;
}

// Role-related types
export interface Role {
  id: string;
  name: string;
  type: string;
  description: string;
  isSystemRole: boolean;
  tenantId?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface RolesResponse {
  data: Role[];
  meta: {
    count: number;
    tenantId?: string;
  };
}

export interface CreateRoleData {
  name: string;
  type: string;
  description: string;
  tenantId?: string;
}

export interface UpdateRoleData {
  name?: string;
  type?: string;
  description?: string;
}

export interface AssignRoleData {
  userId: string;
  roleId: string;
  tenantId?: string;
}

export interface UnassignRoleData {
  userId: string;
  roleId: string;
}

// Permission-related types
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  createdAt: string;
  updatedAt: string;
}

export interface PermissionsResponse {
  data: Permission[];
  meta: {
    count: number;
    userId?: string;
    canBypassTenantScope?: boolean;
  };
}

export interface CreatePermissionData {
  name: string;
  description: string;
  resource: string;
  action: string;
}

// Vehicle Hierarchy types
export interface VehicleYear {
  id: string;
  year: number;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleMake {
  id: string;
  name: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleModel {
  id: string;
  name: string;
  makeId: string;
  tenantId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  make?: VehicleMake;
  years?: VehicleYear[];
}

export interface VehicleModelYear {
  modelId: string;
  yearId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  model?: VehicleModel;
  year?: VehicleYear;
}

export interface VehicleHierarchyData {
  years: Array<
    VehicleYear & {
      makes: Array<
        VehicleMake & {
          models: Array<
            VehicleModel & {
              years: VehicleYear[];
            }
          >;
        }
      >;
    }
  >;
}

export interface YearHierarchy {
  year: VehicleYear;
  makes: Array<
    VehicleMake & {
      models: VehicleModel[];
    }
  >;
}

// Vehicle Hierarchy API request/response types
export interface VehicleYearsResponse {
  data: VehicleYear[];
  meta: {
    count: number;
    tenantId: string;
  };
}

export interface VehicleMakesResponse {
  data: VehicleMake[];
  meta: {
    count: number;
    tenantId: string;
  };
}

export interface VehicleModelsResponse {
  data: VehicleModel[];
  meta: {
    count: number;
    tenantId: string;
    makeId?: string;
    yearId?: string;
  };
}

export interface CreateYearData {
  year: number;
}

export interface CreateMakeData {
  name: string;
}

export interface CreateModelData {
  name: string;
  makeId: string;
  isActive?: boolean;
}

export interface BulkCreateYearsData {
  startYear: number;
  endYear: number;
}

export interface BulkCreateModelsData {
  modelNames: string[];
}

// Engagement-related types
export interface EngagementMetrics {
  dau: number; // Daily Active Users
  wau: number; // Weekly Active Users
  mau: number; // Monthly Active Users
  totalUsers: number;
  inactiveOneWeek: number;
  inactiveOneMonth: number;
}

export interface UserActivitySummary {
  id: string;
  email: string;
  firstName?: string | null;
  lastName?: string | null;
  lastLoginAt?: string | null;
  lastActivityAt?: string | null;
  isActive: boolean;
  createdAt: string;
}

export interface InactiveUsersReport {
  data: {
    users: UserActivitySummary[];
    totalCount: number;
    thresholdDays: number;
  };
  meta: {
    tenantId: string;
    timestamp: string;
  };
}

export interface EngagementMetricsResponse {
  data: EngagementMetrics;
  meta: {
    tenantId: string;
    timestamp: string;
  };
}

export interface InactiveUsersResponse {
  data: InactiveUsersReport;
  meta: {
    tenantId: string;
    thresholdDays: number;
    limit: number;
    offset: number;
    timestamp: string;
  };
}

export interface UserActivityResponse {
  data: {
    users: UserActivitySummary[];
    totalCount: number;
  };
  meta: {
    tenantId: string;
    limit: number;
    offset: number;
    sortBy: string;
    sortOrder: string;
    timestamp: string;
  };
}

export interface AssociateModelYearsData {
  yearIds: string[];
}

export interface BulkAssociateModelYearsData {
  associations: Array<{
    modelId: string;
    yearIds: string[];
  }>;
}

// ===== VEHICLE HIERARCHY V2 TYPES =====

export interface VehicleBrandV2 {
  id: string;
  name: string;
  tenantId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  subBrands?: VehicleSubBrandV2[];
}

export interface VehicleSubBrandV2 {
  id: string;
  name: string;
  brandId: string;
  tenantId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  brand?: VehicleBrandV2;
  models?: VehicleModelV2[];
}

export interface VehicleModelV2 {
  id: string;
  name: string;
  subBrandId: string;
  tenantId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  subBrand?: VehicleSubBrandV2 & {
    brand?: VehicleBrandV2;
  };
  years?: VehicleYear[];
}

export interface VehicleModelYearV2 {
  modelId: string;
  yearId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  model?: VehicleModelV2;
  year?: VehicleYear;
}

export interface VehicleHierarchyDataV2 {
  years: Array<
    VehicleYear & {
      brands: Array<
        VehicleBrandV2 & {
          subBrands: Array<
            VehicleSubBrandV2 & {
              models: Array<
                VehicleModelV2 & {
                  years: VehicleYear[];
                }
              >;
            }
          >;
        }
      >;
    }
  >;
}

// Brand-first hierarchy for tree view
export interface VehicleHierarchyTreeDataV2 {
  brands: Array<
    VehicleBrandV2 & {
      subBrands: Array<
        VehicleSubBrandV2 & {
          models: Array<
            VehicleModelV2 & {
              years: VehicleYear[];
            }
          >;
        }
      >;
    }
  >;
}

export interface YearHierarchyV2 {
  year: VehicleYear;
  brands: Array<
    VehicleBrandV2 & {
      subBrands: Array<
        VehicleSubBrandV2 & {
          models: VehicleModelV2[];
        }
      >;
    }
  >;
}

// Vehicle Hierarchy V2 API request/response types
export interface VehicleBrandsV2Response {
  data: VehicleBrandV2[];
  meta: {
    count: number;
    tenantId: string;
  };
}

export interface VehicleSubBrandsV2Response {
  data: VehicleSubBrandV2[];
  meta: {
    count: number;
    tenantId: string;
    brandId?: string;
  };
}

export interface VehicleModelsV2Response {
  data: VehicleModelV2[];
  meta: {
    count: number;
    tenantId: string;
    subBrandId?: string;
  };
}

export interface CreateBrandV2Data {
  name: string;
  isActive?: boolean;
}

export interface CreateSubBrandV2Data {
  name: string;
  brandId: string;
  isActive?: boolean;
}

export interface CreateModelV2Data {
  name: string;
  subBrandId: string;
  isActive?: boolean;
}

export interface UpdateBrandV2Data {
  name?: string;
  isActive?: boolean;
}

export interface UpdateSubBrandV2Data {
  name?: string;
  isActive?: boolean;
}

export interface UpdateModelV2Data {
  name?: string;
  isActive?: boolean;
}

export interface AssociateModelYearsV2Data {
  yearIds: string[];
}

// Base API client class
class BaseApiClient {
  private getApiUrl(): string {
    const apiUrl = env.apiUrl;

    if (apiUrl) {
      // Ensure the URL has https:// protocol and doesn't end with a slash
      let fullUrl = apiUrl;
      if (!fullUrl.startsWith("http://") && !fullUrl.startsWith("https://")) {
        fullUrl = `https://${fullUrl}`;
      }
      return fullUrl.replace(/\/$/, "");
    }

    // Fallback for development
    return "http://localhost:8081";
  }

  protected async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    getToken: () => Promise<string | null>,
  ): Promise<T> {
    const baseUrl = this.getApiUrl();
    const url = `${baseUrl}${endpoint}`;

    const token = await getToken();

    const headers = {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        error: "Unknown Error",
        message: `HTTP ${response.status}: ${response.statusText}`,
        statusCode: response.status,
        timestamp: new Date().toISOString(),
      }));

      const error = new Error(errorData.message) as Error & ApiError;
      Object.assign(error, errorData);
      throw error;
    }

    return response.json();
  }
}

// Users API client
export class UsersApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  async getAll(): Promise<UsersResponse> {
    return this.makeRequest<UsersResponse>(
      "/api/v1/users",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getById(userId: string): Promise<ApiResponse<User>> {
    return this.makeRequest<ApiResponse<User>>(
      `/api/v1/users/${userId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async update(
    userId: string,
    updateData: UpdateUserData,
  ): Promise<ApiResponse<User>> {
    return this.makeRequest<ApiResponse<User>>(
      `/api/v1/users/${userId}`,
      {
        method: "PUT",
        body: JSON.stringify(updateData),
      },
      this.getToken,
    );
  }

  async delete(userId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/users/${userId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  async toggleStatus(userId: string): Promise<ApiResponse<User>> {
    return this.makeRequest<ApiResponse<User>>(
      `/api/v1/users/${userId}/toggle-status`,
      {
        method: "PATCH",
      },
      this.getToken,
    );
  }
}

// Tenants API client
export class TenantApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  async getAll(): Promise<TenantsResponse> {
    return this.makeRequest<TenantsResponse>(
      "/api/v1/tenants",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getCurrent(): Promise<ApiResponse<Tenant>> {
    // For non-system admins, this will return their own tenant
    const response = await this.getAll();
    if (response.data.length > 0) {
      return { data: response.data[0] };
    }
    throw new Error("No tenant found");
  }

  async getById(tenantId: string): Promise<ApiResponse<Tenant>> {
    return this.makeRequest<ApiResponse<Tenant>>(
      `/api/v1/tenants/${tenantId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async create(data: CreateTenantData): Promise<ApiResponse<Tenant>> {
    return this.makeRequest<ApiResponse<Tenant>>(
      "/api/v1/tenants",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async update(
    tenantId: string,
    data: UpdateTenantData,
  ): Promise<ApiResponse<Tenant>> {
    return this.makeRequest<ApiResponse<Tenant>>(
      `/api/v1/tenants/${tenantId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async delete(tenantId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/tenants/${tenantId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }
}

// Roles API client
export class RolesApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  async getAll(): Promise<RolesResponse> {
    return this.makeRequest<RolesResponse>(
      "/api/v1/roles",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getForTenant(tenantId: string): Promise<RolesResponse> {
    return this.makeRequest<RolesResponse>(
      `/api/v1/roles/tenant/${tenantId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getForUser(userId: string): Promise<RolesResponse> {
    return this.makeRequest<RolesResponse>(
      `/api/v1/roles/user/${userId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async create(data: CreateRoleData): Promise<ApiResponse<Role>> {
    return this.makeRequest<ApiResponse<Role>>(
      "/api/v1/roles",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async update(
    roleId: string,
    data: UpdateRoleData,
  ): Promise<ApiResponse<Role>> {
    return this.makeRequest<ApiResponse<Role>>(
      `/api/v1/roles/${roleId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async delete(roleId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/roles/${roleId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  async assignToUser(
    data: AssignRoleData,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      "/api/v1/roles/assign",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async removeFromUser(
    data: UnassignRoleData,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      "/api/v1/roles/unassign",
      {
        method: "DELETE",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }
}

// Vehicle Hierarchy API client
export class VehicleHierarchyApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  // Year operations
  async getYears(): Promise<VehicleYearsResponse> {
    return this.makeRequest<VehicleYearsResponse>(
      "/api/v1/vehicle-hierarchy/years",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createYear(data: CreateYearData): Promise<ApiResponse<VehicleYear>> {
    return this.makeRequest<ApiResponse<VehicleYear>>(
      "/api/v1/vehicle-hierarchy/years",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async bulkCreateYears(
    data: BulkCreateYearsData,
  ): Promise<ApiResponse<VehicleYear[]>> {
    return this.makeRequest<ApiResponse<VehicleYear[]>>(
      "/api/v1/vehicle-hierarchy/years/bulk-create",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteYear(yearId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy/years/${yearId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Make operations
  async getMakes(): Promise<VehicleMakesResponse> {
    return this.makeRequest<VehicleMakesResponse>(
      "/api/v1/vehicle-hierarchy/makes",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createMake(data: CreateMakeData): Promise<ApiResponse<VehicleMake>> {
    return this.makeRequest<ApiResponse<VehicleMake>>(
      "/api/v1/vehicle-hierarchy/makes",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async bulkCreateModels(
    makeId: string,
    data: BulkCreateModelsData,
  ): Promise<ApiResponse<VehicleModel[]>> {
    return this.makeRequest<ApiResponse<VehicleModel[]>>(
      `/api/v1/vehicle-hierarchy/makes/${makeId}/models/bulk-create`,
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async updateMake(
    makeId: string,
    data: { name: string },
  ): Promise<ApiResponse<VehicleMake>> {
    return this.makeRequest<ApiResponse<VehicleMake>>(
      `/api/v1/vehicle-hierarchy/makes/${makeId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteMake(makeId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy/makes/${makeId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Model operations
  async getModels(makeId: string): Promise<VehicleModelsResponse> {
    return this.makeRequest<VehicleModelsResponse>(
      `/api/v1/vehicle-hierarchy/models?makeId=${makeId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getAllModels(): Promise<VehicleModelsResponse> {
    return this.makeRequest<VehicleModelsResponse>(
      "/api/v1/vehicle-hierarchy/models/all",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createModel(data: CreateModelData): Promise<ApiResponse<VehicleModel>> {
    return this.makeRequest<ApiResponse<VehicleModel>>(
      "/api/v1/vehicle-hierarchy/models",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async associateModelWithYears(
    modelId: string,
    data: AssociateModelYearsData,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy/models/${modelId}/years/associate`,
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async removeModelYearAssociation(
    modelId: string,
    yearId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy/models/${modelId}/years/${yearId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  async updateModel(
    modelId: string,
    data: { name: string },
  ): Promise<ApiResponse<VehicleModel>> {
    return this.makeRequest<ApiResponse<VehicleModel>>(
      `/api/v1/vehicle-hierarchy/models/${modelId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteModel(
    modelId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy/models/${modelId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Hierarchy query operations
  async getFullHierarchy(): Promise<ApiResponse<VehicleHierarchyData>> {
    return this.makeRequest<ApiResponse<VehicleHierarchyData>>(
      "/api/v1/vehicle-hierarchy/full",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getHierarchyByYear(year: number): Promise<ApiResponse<YearHierarchy>> {
    return this.makeRequest<ApiResponse<YearHierarchy>>(
      `/api/v1/vehicle-hierarchy/by-year/${year}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getModelsByYear(yearId: string): Promise<VehicleModelsResponse> {
    return this.makeRequest<VehicleModelsResponse>(
      `/api/v1/vehicle-hierarchy/models/by-year/${yearId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getYearsByModel(modelId: string): Promise<VehicleYearsResponse> {
    return this.makeRequest<VehicleYearsResponse>(
      `/api/v1/vehicle-hierarchy/models/${modelId}/years`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async bulkAssociateModelYears(
    data: BulkAssociateModelYearsData,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      "/api/v1/vehicle-hierarchy/models/bulk-associate-years",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  // Export operations
  async exportToCSV(): Promise<string> {
    const token = await this.getToken();
    const response = await fetch("/api/v1/vehicle-hierarchy/export/csv", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }

    return response.text();
  }

  async exportToJSON(): Promise<Blob> {
    const token = await this.getToken();
    const response = await fetch("/api/v1/vehicle-hierarchy/export/json", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }

    return response.blob();
  }
}

// Vehicle Hierarchy V2 API client
export class VehicleHierarchyV2ApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  // Brand operations
  async getBrands(): Promise<VehicleBrandsV2Response> {
    return this.makeRequest<VehicleBrandsV2Response>(
      "/api/v1/vehicle-hierarchy-v2/brands",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createBrand(
    data: CreateBrandV2Data,
  ): Promise<ApiResponse<VehicleBrandV2>> {
    return this.makeRequest<ApiResponse<VehicleBrandV2>>(
      "/api/v1/vehicle-hierarchy-v2/brands",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async updateBrand(
    brandId: string,
    data: UpdateBrandV2Data,
  ): Promise<ApiResponse<VehicleBrandV2>> {
    return this.makeRequest<ApiResponse<VehicleBrandV2>>(
      `/api/v1/vehicle-hierarchy-v2/brands/${brandId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteBrand(
    brandId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v2/brands/${brandId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Sub-brand operations
  async getSubBrands(brandId?: string): Promise<VehicleSubBrandsV2Response> {
    const params = brandId ? `?brandId=${brandId}` : "";
    return this.makeRequest<VehicleSubBrandsV2Response>(
      `/api/v1/vehicle-hierarchy-v2/sub-brands${params}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getSubBrandsByBrand(
    brandId: string,
  ): Promise<VehicleSubBrandsV2Response> {
    return this.makeRequest<VehicleSubBrandsV2Response>(
      `/api/v1/vehicle-hierarchy-v2/brands/${brandId}/sub-brands`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createSubBrand(
    data: CreateSubBrandV2Data,
  ): Promise<ApiResponse<VehicleSubBrandV2>> {
    return this.makeRequest<ApiResponse<VehicleSubBrandV2>>(
      "/api/v1/vehicle-hierarchy-v2/sub-brands",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async updateSubBrand(
    subBrandId: string,
    data: UpdateSubBrandV2Data,
  ): Promise<ApiResponse<VehicleSubBrandV2>> {
    return this.makeRequest<ApiResponse<VehicleSubBrandV2>>(
      `/api/v1/vehicle-hierarchy-v2/sub-brands/${subBrandId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteSubBrand(
    subBrandId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v2/sub-brands/${subBrandId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Model operations
  async getModels(subBrandId?: string): Promise<VehicleModelsV2Response> {
    const params = subBrandId ? `?subBrandId=${subBrandId}` : "";
    return this.makeRequest<VehicleModelsV2Response>(
      `/api/v1/vehicle-hierarchy-v2/models${params}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getModelsBySubBrand(
    subBrandId: string,
  ): Promise<VehicleModelsV2Response> {
    return this.makeRequest<VehicleModelsV2Response>(
      `/api/v1/vehicle-hierarchy-v2/sub-brands/${subBrandId}/models`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createModel(
    data: CreateModelV2Data,
  ): Promise<ApiResponse<VehicleModelV2>> {
    return this.makeRequest<ApiResponse<VehicleModelV2>>(
      "/api/v1/vehicle-hierarchy-v2/models",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async updateModel(
    modelId: string,
    data: UpdateModelV2Data,
  ): Promise<ApiResponse<VehicleModelV2>> {
    return this.makeRequest<ApiResponse<VehicleModelV2>>(
      `/api/v1/vehicle-hierarchy-v2/models/${modelId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteModel(
    modelId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v2/models/${modelId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Model-year association operations
  async getYearsByModel(modelId: string): Promise<VehicleYearsResponse> {
    return this.makeRequest<VehicleYearsResponse>(
      `/api/v1/vehicle-hierarchy-v2/models/${modelId}/years`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async associateModelWithYears(
    modelId: string,
    data: AssociateModelYearsV2Data,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v2/models/${modelId}/years/associate`,
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async removeModelYearAssociations(
    modelId: string,
    data: AssociateModelYearsV2Data,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v2/models/${modelId}/years/remove`,
      {
        method: "DELETE",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  // Hierarchy query operations
  async getFullHierarchy(): Promise<ApiResponse<VehicleHierarchyDataV2>> {
    return this.makeRequest<ApiResponse<VehicleHierarchyDataV2>>(
      "/api/v1/vehicle-hierarchy-v2/full",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getHierarchyByYear(
    year: number,
  ): Promise<ApiResponse<YearHierarchyV2>> {
    return this.makeRequest<ApiResponse<YearHierarchyV2>>(
      `/api/v1/vehicle-hierarchy-v2/by-year/${year}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getModelsByYear(yearId: string): Promise<VehicleModelsV2Response> {
    return this.makeRequest<VehicleModelsV2Response>(
      `/api/v1/vehicle-hierarchy-v2/models/by-year/${yearId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }
}

// ===== VEHICLE HIERARCHY V3 TYPES =====

export interface VehicleYearV3 {
  id: string;
  name: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface VehicleBrandV3 {
  id: string;
  name: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  subBrands?: VehicleSubBrandV3[];
}

export interface VehicleSubBrandV3 {
  id: string;
  name: string;
  brandId: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  brand?: VehicleBrandV3;
  models?: VehicleModelV3[];
}

export interface VehicleModelV3 {
  id: string;
  name: string;
  subBrandId: string;
  tenantId: string;
  isActive: boolean;
  displayOrder: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  subBrand?: VehicleSubBrandV3 & {
    brand?: VehicleBrandV3;
  };
  years?: VehicleYearV3[];
  modelYears?: VehicleModelYearV3[];
}

export interface VehicleModelYearV3 {
  id: string;
  modelId: string;
  yearId: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  model?: VehicleModelV3;
  year?: VehicleYearV3;
}

export interface ModelYearV3WithRelations extends VehicleModelYearV3 {
  year: VehicleYearV3;
}

// Vehicle Hierarchy V3 API request/response types
export interface VehicleYearsV3Response {
  data: VehicleYearV3[];
  meta: {
    count: number;
    tenantId: string;
  };
}

export interface VehicleBrandsV3Response {
  data: VehicleBrandV3[];
  meta: {
    count: number;
    tenantId: string;
  };
}

export interface VehicleSubBrandsV3Response {
  data: VehicleSubBrandV3[];
  meta: {
    count: number;
    tenantId: string;
    brandId?: string;
  };
}

export interface VehicleModelsV3Response {
  data: VehicleModelV3[];
  meta: {
    count: number;
    tenantId: string;
    subBrandId?: string;
  };
}

export interface CreateYearV3Data {
  name: string;
}

export interface UpdateYearV3Data {
  name?: string;
  isActive?: boolean;
  displayOrder?: number;
}

export interface CreateBrandV3Data {
  name: string;
}

export interface UpdateBrandV3Data {
  name?: string;
  isActive?: boolean;
  displayOrder?: number;
}

export interface CreateSubBrandV3Data {
  name: string;
}

export interface UpdateSubBrandV3Data {
  name?: string;
  brandId?: string;
  isActive?: boolean;
  displayOrder?: number;
}

export interface CreateModelV3Data {
  name: string;
}

export interface UpdateModelV3Data {
  name?: string;
  subBrandId?: string;
  isActive?: boolean;
  displayOrder?: number;
}

export interface ReorderYearsV3Data {
  yearOrders: Array<{
    id: string;
    displayOrder: number;
  }>;
}

export interface ReorderBrandsV3Data {
  brandOrders: Array<{
    id: string;
    displayOrder: number;
  }>;
}

export interface ReorderSubBrandsV3Data {
  subBrandOrders: Array<{
    id: string;
    displayOrder: number;
  }>;
}

export interface ReorderModelsV3Data {
  modelOrders: Array<{
    id: string;
    displayOrder: number;
  }>;
}

export interface CreateModelYearV3Data {
  modelId: string;
  yearId: string;
}

// Vehicle Hierarchy V3 API client
export class VehicleHierarchyV3ApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  // Year operations
  async getYears(): Promise<VehicleYearsV3Response> {
    return this.makeRequest<VehicleYearsV3Response>(
      "/api/v1/vehicle-hierarchy-v3/years",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getYear(yearId: string): Promise<ApiResponse<VehicleYearV3>> {
    return this.makeRequest<ApiResponse<VehicleYearV3>>(
      `/api/v1/vehicle-hierarchy-v3/years/${yearId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createYear(data: CreateYearV3Data): Promise<ApiResponse<VehicleYearV3>> {
    return this.makeRequest<ApiResponse<VehicleYearV3>>(
      "/api/v1/vehicle-hierarchy-v3/years",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async updateYear(
    yearId: string,
    data: UpdateYearV3Data,
  ): Promise<ApiResponse<VehicleYearV3>> {
    return this.makeRequest<ApiResponse<VehicleYearV3>>(
      `/api/v1/vehicle-hierarchy-v3/years/${yearId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteYear(yearId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v3/years/${yearId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  async reorderYears(
    data: ReorderYearsV3Data,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      "/api/v1/vehicle-hierarchy-v3/years/reorder",
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  // Brand operations (placeholder for future phases)
  async getBrands(): Promise<VehicleBrandsV3Response> {
    return this.makeRequest<VehicleBrandsV3Response>(
      "/api/v1/vehicle-hierarchy-v3/brands",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createBrand(
    data: CreateBrandV3Data,
  ): Promise<ApiResponse<VehicleBrandV3>> {
    return this.makeRequest<ApiResponse<VehicleBrandV3>>(
      "/api/v1/vehicle-hierarchy-v3/brands",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async getBrand(brandId: string): Promise<ApiResponse<VehicleBrandV3>> {
    return this.makeRequest<ApiResponse<VehicleBrandV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async updateBrand(
    brandId: string,
    data: UpdateBrandV3Data,
  ): Promise<ApiResponse<VehicleBrandV3>> {
    return this.makeRequest<ApiResponse<VehicleBrandV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteBrand(brandId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Sub-brand operations
  async getSubBrands(brandId: string): Promise<VehicleSubBrandsV3Response> {
    return this.makeRequest<VehicleSubBrandsV3Response>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getSubBrandById(
    brandId: string,
    subBrandId: string,
  ): Promise<ApiResponse<VehicleSubBrandV3>> {
    return this.makeRequest<ApiResponse<VehicleSubBrandV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createSubBrand(
    brandId: string,
    data: CreateSubBrandV3Data,
  ): Promise<ApiResponse<VehicleSubBrandV3>> {
    return this.makeRequest<ApiResponse<VehicleSubBrandV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands`,
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async updateSubBrand(
    brandId: string,
    subBrandId: string,
    data: UpdateSubBrandV3Data,
  ): Promise<ApiResponse<VehicleSubBrandV3>> {
    return this.makeRequest<ApiResponse<VehicleSubBrandV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteSubBrand(
    brandId: string,
    subBrandId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Helper method to get all sub-brands across all brands with brand information
  async getAllSubBrands(): Promise<VehicleSubBrandsV3Response> {
    // First get all brands
    const brandsResponse = await this.getBrands();

    // Then get sub-brands for each brand
    const allSubBrandsPromises = brandsResponse.data.map(async (brand) => {
      const subBrandsResponse = await this.getSubBrands(brand.id);
      // Add brand information to each sub-brand
      return subBrandsResponse.data.map(subBrand => ({
        ...subBrand,
        brand: brand,
      }));
    });

    const allSubBrandsArrays = await Promise.all(allSubBrandsPromises);
    const allSubBrands = allSubBrandsArrays.flat();

    return {
      data: allSubBrands,
      meta: {
        count: allSubBrands.length,
        tenantId: brandsResponse.meta.tenantId,
      },
    };
  }

  // Model operations
  async getModels(
    brandId: string,
    subBrandId: string,
  ): Promise<VehicleModelsV3Response> {
    return this.makeRequest<VehicleModelsV3Response>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getModelById(
    brandId: string,
    subBrandId: string,
    modelId: string,
  ): Promise<ApiResponse<VehicleModelV3>> {
    return this.makeRequest<ApiResponse<VehicleModelV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createModel(
    brandId: string,
    subBrandId: string,
    data: CreateModelV3Data,
  ): Promise<ApiResponse<VehicleModelV3>> {
    return this.makeRequest<ApiResponse<VehicleModelV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models`,
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async updateModel(
    brandId: string,
    subBrandId: string,
    modelId: string,
    data: UpdateModelV3Data,
  ): Promise<ApiResponse<VehicleModelV3>> {
    return this.makeRequest<ApiResponse<VehicleModelV3>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteModel(
    brandId: string,
    subBrandId: string,
    modelId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v3/brands/${brandId}/sub-brands/${subBrandId}/models/${modelId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  async getYearsByModel(
    modelId: string,
  ): Promise<ApiResponse<ModelYearV3WithRelations[]>> {
    return this.makeRequest<ApiResponse<ModelYearV3WithRelations[]>>(
      `/api/v1/vehicle-hierarchy-v3/models/${modelId}/years`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async createModelYear(
    modelId: string,
    data: CreateModelYearV3Data,
  ): Promise<ApiResponse<VehicleModelYearV3>> {
    return this.makeRequest<ApiResponse<VehicleModelYearV3>>(
      `/api/v1/vehicle-hierarchy-v3/models/${modelId}/years`,
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async deleteModelYear(
    modelId: string,
    yearId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/vehicle-hierarchy-v3/models/${modelId}/years/${yearId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }

  // Helper method to get all models across all sub-brands with brand and sub-brand information
  async getAllModels(): Promise<VehicleModelsV3Response> {
    // First get all brands
    const brandsResponse = await this.getBrands();

    // Then get all sub-brands for each brand
    const allModelsPromises = brandsResponse.data.map(async (brand) => {
      const subBrandsResponse = await this.getSubBrands(brand.id);

      // Get models for each sub-brand
      const modelsPromises = subBrandsResponse.data.map(async (subBrand) => {
        const modelsResponse = await this.getModels(brand.id, subBrand.id);
        // Add brand and sub-brand information to each model using the correct nested structure
        return modelsResponse.data.map(model => ({
          ...model,
          subBrand: {
            ...subBrand,
            brand: brand,
          },
        }));
      });

      const subBrandModelsArrays = await Promise.all(modelsPromises);
      return subBrandModelsArrays.flat();
    });

    const allModelsArrays = await Promise.all(allModelsPromises);
    const allModels = allModelsArrays.flat();

    return {
      data: allModels,
      meta: {
        count: allModels.length,
        tenantId: brandsResponse.meta.tenantId,
      },
    };
  }
}

// Engagement API client
export class EngagementApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  async getMetrics(tenantId?: string): Promise<EngagementMetricsResponse> {
    const params = tenantId ? `?tenantId=${encodeURIComponent(tenantId)}` : "";
    return this.makeRequest<EngagementMetricsResponse>(
      `/api/v1/engagement/metrics${params}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getInactiveUsers(
    thresholdDays: number,
    limit: number,
    offset: number,
    tenantId?: string
  ): Promise<InactiveUsersReport> {
    const params = new URLSearchParams();
    params.append('thresholdDays', thresholdDays.toString());
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    if (tenantId) {
      params.append('tenantId', tenantId);
    }

    return this.makeRequest<InactiveUsersReport>(
      `/api/v1/engagement/inactive-users?${params.toString()}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getUserActivity(
    limit: number,
    offset: number,
    sortBy: string,
    sortOrder: string,
    tenantId?: string
  ): Promise<UserActivityResponse> {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    params.append('sortBy', sortBy);
    params.append('sortOrder', sortOrder);
    if (tenantId) {
      params.append('tenantId', tenantId);
    }

    return this.makeRequest<UserActivityResponse>(
      `/api/v1/engagement/user-activity?${params.toString()}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }
}

// ===== DOCUMENT TYPES =====

export interface Document {
  id: string;
  tenantId: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  s3Key: string;
  title?: string;
  description?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  creator: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateDocumentRequest {
  fileName: string;
  originalName?: string;
  fileSize: number;
  mimeType: string;
  title?: string;
  description?: string;
}

export interface CreateDocumentResponse {
  id: string;
  tenantId: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  s3Key: string;
  title?: string;
  description?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  uploadUrl: string;
  expiresIn: number;
}

export interface GetDocumentsOptions {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'createdAt' | 'fileName' | 'fileSize';
  sortOrder?: 'asc' | 'desc';
}

export interface GetDocumentsResponse {
  documents: Document[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface GetDownloadUrlResponse {
  downloadUrl: string;
  expiresIn: number;
}

// Documents API client
export class DocumentsApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  async createUploadUrl(request: CreateDocumentRequest): Promise<ApiResponse<CreateDocumentResponse>> {
    // Set originalName to fileName if not provided
    const requestWithDefaults = {
      ...request,
      originalName: request.originalName || request.fileName,
    };

    return this.makeRequest<ApiResponse<CreateDocumentResponse>>(
      '/api/v1/documents/upload-url',
      {
        method: 'POST',
        body: JSON.stringify(requestWithDefaults),
      },
      this.getToken,
    );
  }

  async getDocuments(options: GetDocumentsOptions = {}): Promise<ApiResponse<GetDocumentsResponse>> {
    const params = new URLSearchParams();
    if (options.page) params.append('page', options.page.toString());
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.search) params.append('search', options.search);
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortOrder) params.append('sortOrder', options.sortOrder);

    const queryString = params.toString();
    const endpoint = `/api/v1/documents${queryString ? `?${queryString}` : ''}`;

    return this.makeRequest<ApiResponse<GetDocumentsResponse>>(
      endpoint,
      {
        method: 'GET',
      },
      this.getToken,
    );
  }

  async getDownloadUrl(documentId: string): Promise<ApiResponse<GetDownloadUrlResponse>> {
    return this.makeRequest<ApiResponse<GetDownloadUrlResponse>>(
      `/api/v1/documents/${documentId}/url`,
      {
        method: 'GET',
      },
      this.getToken,
    );
  }

  async deleteDocument(documentId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/documents/${documentId}`,
      {
        method: 'DELETE',
      },
      this.getToken,
    );
  }

  async getInactiveUsers(
    thresholdDays: number,
    limit: number = 50,
    offset: number = 0,
    tenantId?: string,
  ): Promise<InactiveUsersResponse> {
    const params = new URLSearchParams({
      thresholdDays: thresholdDays.toString(),
      limit: limit.toString(),
      offset: offset.toString(),
    });

    if (tenantId) {
      params.append("tenantId", tenantId);
    }

    return this.makeRequest<InactiveUsersResponse>(
      `/api/v1/engagement/inactive-users?${params}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getUserActivity(
    limit: number = 50,
    offset: number = 0,
    sortBy: "lastLoginAt" | "lastActivityAt" | "email" = "lastActivityAt",
    sortOrder: "asc" | "desc" = "desc",
    tenantId?: string,
  ): Promise<UserActivityResponse> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
      sortBy,
      sortOrder,
    });

    if (tenantId) {
      params.append("tenantId", tenantId);
    }

    return this.makeRequest<UserActivityResponse>(
      `/api/v1/engagement/user-activity?${params}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }
}

// Permissions API client
export class PermissionsApiClient extends BaseApiClient {
  private getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
  }

  async getAll(): Promise<PermissionsResponse> {
    return this.makeRequest<PermissionsResponse>(
      "/api/v1/permissions",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getForUser(userId: string): Promise<PermissionsResponse> {
    return this.makeRequest<PermissionsResponse>(
      `/api/v1/permissions/user/${userId}`,
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async getCurrent(): Promise<PermissionsResponse> {
    return this.makeRequest<PermissionsResponse>(
      "/api/v1/permissions/me",
      {
        method: "GET",
      },
      this.getToken,
    );
  }

  async create(data: CreatePermissionData): Promise<ApiResponse<Permission>> {
    return this.makeRequest<ApiResponse<Permission>>(
      "/api/v1/permissions",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async update(
    permissionId: string,
    data: Partial<CreatePermissionData>,
  ): Promise<ApiResponse<Permission>> {
    return this.makeRequest<ApiResponse<Permission>>(
      `/api/v1/permissions/${permissionId}`,
      {
        method: "PUT",
        body: JSON.stringify(data),
      },
      this.getToken,
    );
  }

  async delete(
    permissionId: string,
  ): Promise<ApiResponse<{ success: boolean }>> {
    return this.makeRequest<ApiResponse<{ success: boolean }>>(
      `/api/v1/permissions/${permissionId}`,
      {
        method: "DELETE",
      },
      this.getToken,
    );
  }
}

// Main typed API client
export class TypedApiClient extends BaseApiClient {
  public users: UsersApiClient;
  public tenants: TenantApiClient;
  public roles: RolesApiClient;
  public permissions: PermissionsApiClient;
  public vehicleHierarchy: VehicleHierarchyApiClient;
  public vehicleHierarchyV2: VehicleHierarchyV2ApiClient;
  public vehicleHierarchyV3: VehicleHierarchyV3ApiClient;
  public engagement: EngagementApiClient;
  public documents: DocumentsApiClient;
  public getToken: () => Promise<string | null>;

  constructor(getToken: () => Promise<string | null>) {
    super();
    this.getToken = getToken;
    this.users = new UsersApiClient(getToken);
    this.tenants = new TenantApiClient(getToken);
    this.roles = new RolesApiClient(getToken);
    this.permissions = new PermissionsApiClient(getToken);
    this.vehicleHierarchy = new VehicleHierarchyApiClient(getToken);
    this.vehicleHierarchyV2 = new VehicleHierarchyV2ApiClient(getToken);
    this.vehicleHierarchyV3 = new VehicleHierarchyV3ApiClient(getToken);
    this.engagement = new EngagementApiClient(getToken);
    this.documents = new DocumentsApiClient(getToken);
  }

  // Keep the generic makeRequest method for backward compatibility
  async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    return super.makeRequest<T>(endpoint, options, this.getToken);
  }
}

// Hook to get typed API client instance
export function useTypedApi(): TypedApiClient {
  const { getToken } = useClerkAuth();
  return new TypedApiClient(getToken);
}

// Export for backward compatibility
export { TypedApiClient as ApiClient };
