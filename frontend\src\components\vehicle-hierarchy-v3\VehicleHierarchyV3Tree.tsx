import React, { useState } from "react";
import { <PERSON><PERSON>, Badge, CompanyAdminOnly } from "../index";
import type {
  VehicleHierarchyDataV3,
  VehicleYearV3,
  VehicleBrandV3,
  VehicleSubBrandV3,
  VehicleModelV3,
} from "../../services/api-client";
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  Building2,
  Tag,
  Car,
  Plus,
  Trash2,
} from "lucide-react";

interface VehicleHierarchyV3TreeProps {
  hierarchy: VehicleHierarchyDataV3;
  onAddModels: (year: VehicleYearV3) => void;
  onDeleteYear?: (year: VehicleYearV3) => void;
  onDeleteBrand?: (brand: VehicleBrandV3) => void;
  onDeleteSubBrand?: (subBrand: VehicleSubBrandV3) => void;
  onDeleteModel?: (model: VehicleModelV3) => void;
  searchQuery?: string;
}

export const VehicleHierarchyV3Tree: React.FC<VehicleHierarchyV3TreeProps> =
  React.memo(
    ({
      hierarchy,
      onAddModels,
      onDeleteYear,
      onD<PERSON>teBrand,
      onDeleteSubBrand,
      onDeleteModel,
      searchQuery = "",
    }) => {
      const [expandedYears, setExpandedYears] = useState<Set<string>>(
        new Set(),
      );
      const [expandedBrands, setExpandedBrands] = useState<Set<string>>(
        new Set(),
      );
      const [expandedSubBrands, setExpandedSubBrands] = useState<Set<string>>(
        new Set(),
      );

      const toggleYear = (yearId: string) => {
        const newExpanded = new Set(expandedYears);
        if (newExpanded.has(yearId)) {
          newExpanded.delete(yearId);
        } else {
          newExpanded.add(yearId);
        }
        setExpandedYears(newExpanded);
      };

      const toggleBrand = (brandId: string) => {
        const newExpanded = new Set(expandedBrands);
        if (newExpanded.has(brandId)) {
          newExpanded.delete(brandId);
        } else {
          newExpanded.add(brandId);
        }
        setExpandedBrands(newExpanded);
      };

      const toggleSubBrand = (subBrandId: string) => {
        const newExpanded = new Set(expandedSubBrands);
        if (newExpanded.has(subBrandId)) {
          newExpanded.delete(subBrandId);
        } else {
          newExpanded.add(subBrandId);
        }
        setExpandedSubBrands(newExpanded);
      };

      const expandAll = () => {
        const allYearIds = new Set(hierarchy.years.map((year) => year.id));
        const allBrandIds = new Set(
          hierarchy.years.flatMap((year) => year.brands.map((brand) => brand.id)),
        );
        const allSubBrandIds = new Set(
          hierarchy.years.flatMap((year) =>
            year.brands.flatMap((brand) =>
              brand.subBrands.map((subBrand) => subBrand.id),
            ),
          ),
        );
        setExpandedYears(allYearIds);
        setExpandedBrands(allBrandIds);
        setExpandedSubBrands(allSubBrandIds);
      };

      const collapseAll = () => {
        setExpandedYears(new Set());
        setExpandedBrands(new Set());
        setExpandedSubBrands(new Set());
      };

      // Search highlighting utility
      const highlightText = (text: string, query: string) => {
        if (!query.trim()) return text;

        const regex = new RegExp(
          `(${query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
          "gi",
        );
        const parts = text.split(regex);

        return parts.map((part, index) =>
          regex.test(part) ? (
            <mark key={index} className="bg-yellow-200 px-1 rounded">
              {part}
            </mark>
          ) : (
            part
          ),
        );
      };

      if (!hierarchy.years.length) {
        return (
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Years Found
            </h3>
            <p className="text-gray-600 mb-4">
              Start by creating your first year to build the vehicle hierarchy.
            </p>
          </div>
        );
      }

      return (
        <div className="space-y-4">
          {/* Tree Controls */}
          <div className="flex items-center justify-between border-b border-gray-200 pb-4">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={expandAll}>
                Expand All
              </Button>
              <Button variant="outline" size="sm" onClick={collapseAll}>
                Collapse All
              </Button>
            </div>
            <div className="text-sm text-gray-600">
              {hierarchy.years.length} years,{" "}
              {hierarchy.years.reduce(
                (total, year) => total + year.brands.length,
                0,
              )}{" "}
              brands,{" "}
              {hierarchy.years.reduce(
                (total, year) =>
                  total +
                  year.brands.reduce(
                    (brandTotal, brand) => brandTotal + brand.subBrands.length,
                    0,
                  ),
                0,
              )}{" "}
              sub-brands,{" "}
              {hierarchy.years.reduce(
                (total, year) =>
                  total +
                  year.brands.reduce(
                    (brandTotal, brand) =>
                      brandTotal +
                      brand.subBrands.reduce(
                        (subBrandTotal, subBrand) =>
                          subBrandTotal + (subBrand.models?.length || 0),
                        0,
                      ),
                    0,
                  ),
                0,
              )}{" "}
              models
            </div>
          </div>

          {/* Tree Structure */}
          <div className="space-y-2">
            {hierarchy.years
              .sort((a, b) => parseInt(b.name) - parseInt(a.name)) // Sort years descending
              .map((year) => (
                <div
                  key={year.id}
                  className="border border-gray-200 rounded-lg"
                >
                  {/* Year Level */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => toggleYear(year.id)}
                        className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
                        disabled={!year.brands.length}
                      >
                        {year.brands.length > 0 &&
                          (expandedYears.has(year.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          ))}
                      </button>
                      <Calendar className="h-5 w-5 text-primary-600" />
                      <span className="font-semibold text-gray-900">
                        {highlightText(year.name, searchQuery)}
                      </span>
                      <Badge variant="secondary">
                        {year.brands.length} brand
                        {year.brands.length !== 1 ? "s" : ""}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-2">
                      <CompanyAdminOnly>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onAddModels(year)}
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          title="Add models to this year"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Models
                        </Button>
                        {onDeleteYear && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onDeleteYear(year)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </CompanyAdminOnly>
                    </div>
                  </div>

                  {/* Brands Level */}
                  {expandedYears.has(year.id) && year.brands.length > 0 && (
                    <div className="border-t border-gray-200">
                      {year.brands
                        .sort((a, b) => {
                          if (a.displayOrder !== b.displayOrder) {
                            return a.displayOrder - b.displayOrder;
                          }
                          return a.name.localeCompare(b.name);
                        })
                        .map((brand) => (
                          <div
                            key={brand.id}
                            className="border-b border-gray-100 last:border-b-0"
                          >
                            {/* Brand Header */}
                            <div className="flex items-center justify-between p-4 pl-12 bg-white hover:bg-gray-50 transition-colors">
                              <div className="flex items-center space-x-3">
                                <button
                                  onClick={() => toggleBrand(brand.id)}
                                  className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
                                  disabled={!brand.subBrands.length}
                                >
                                  {brand.subBrands.length > 0 &&
                                    (expandedBrands.has(brand.id) ? (
                                      <ChevronDown className="h-4 w-4" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4" />
                                    ))}
                                </button>
                                <Building2 className="h-5 w-5 text-blue-600" />
                                <span className="font-medium text-gray-900">
                                  {highlightText(brand.name, searchQuery)}
                                </span>
                                <Badge variant="outline">
                                  {brand.subBrands.length} sub-brand
                                  {brand.subBrands.length !== 1 ? "s" : ""}
                                </Badge>
                              </div>

                              <CompanyAdminOnly>
                                {onDeleteBrand && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onDeleteBrand(brand)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </CompanyAdminOnly>
                            </div>

                            {/* Sub-Brands Level */}
                            {expandedBrands.has(brand.id) && brand.subBrands.length > 0 && (
                              <div className="bg-gray-25">
                                {brand.subBrands
                                  .sort((a, b) => {
                                    if (a.displayOrder !== b.displayOrder) {
                                      return a.displayOrder - b.displayOrder;
                                    }
                                    return a.name.localeCompare(b.name);
                                  })
                                  .map((subBrand) => (
                                    <div
                                      key={subBrand.id}
                                      className="border-b border-gray-100 last:border-b-0"
                                    >
                                      {/* Sub-Brand Header */}
                                      <div className="flex items-center justify-between p-3 pl-20 bg-white hover:bg-gray-50 transition-colors">
                                        <div className="flex items-center space-x-3">
                                          <button
                                            onClick={() => toggleSubBrand(subBrand.id)}
                                            className="flex items-center justify-center w-6 h-6 rounded hover:bg-gray-200 transition-colors"
                                            disabled={!(subBrand.models?.length || 0)}
                                          >
                                            {(subBrand.models?.length || 0) > 0 &&
                                              (expandedSubBrands.has(subBrand.id) ? (
                                                <ChevronDown className="h-4 w-4" />
                                              ) : (
                                                <ChevronRight className="h-4 w-4" />
                                              ))}
                                          </button>
                                          <Tag className="h-4 w-4 text-green-600" />
                                          <span className="text-gray-900">
                                            {highlightText(subBrand.name, searchQuery)}
                                          </span>
                                          <Badge variant="outline" className="text-xs">
                                            {subBrand.models?.length || 0} model
                                            {(subBrand.models?.length || 0) !== 1 ? "s" : ""}
                                          </Badge>
                                        </div>

                                        <CompanyAdminOnly>
                                          {onDeleteSubBrand && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => onDeleteSubBrand(subBrand)}
                                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                            >
                                              <Trash2 className="h-3 w-3" />
                                            </Button>
                                          )}
                                        </CompanyAdminOnly>
                                      </div>

                                      {/* Models Level */}
                                      {expandedSubBrands.has(subBrand.id) && (subBrand.models?.length || 0) > 0 && (
                                        <div className="bg-gray-50">
                                          {subBrand.models!
                                            .sort((a, b) => {
                                              if (a.displayOrder !== b.displayOrder) {
                                                return a.displayOrder - b.displayOrder;
                                              }
                                              return a.name.localeCompare(b.name);
                                            })
                                            .map((model) => (
                                              <div
                                                key={model.id}
                                                className="flex items-center justify-between p-3 pl-28 border-b border-gray-100 last:border-b-0 hover:bg-gray-100 transition-colors"
                                              >
                                                <div className="flex items-center space-x-3">
                                                  <Car className="h-4 w-4 text-purple-600" />
                                                  <span className="text-gray-900">
                                                    {highlightText(model.name, searchQuery)}
                                                  </span>
                                                  {!model.isActive && (
                                                    <Badge variant="warning">
                                                      Inactive
                                                    </Badge>
                                                  )}
                                                  <Badge variant="secondary" className="text-xs">
                                                    {model.years?.length || 0} year
                                                    {(model.years?.length || 0) !== 1 ? "s" : ""}
                                                  </Badge>
                                                </div>

                                                <CompanyAdminOnly>
                                                  {onDeleteModel && (
                                                    <Button
                                                      variant="ghost"
                                                      size="sm"
                                                      onClick={() => onDeleteModel(model)}
                                                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                                    >
                                                      <Trash2 className="h-3 w-3" />
                                                    </Button>
                                                  )}
                                                </CompanyAdminOnly>
                                              </div>
                                            ))}
                                        </div>
                                      )}

                                      {/* Empty state for sub-brand with no models */}
                                      {expandedSubBrands.has(subBrand.id) && (subBrand.models?.length || 0) === 0 && (
                                        <div className="p-6 pl-28 text-center text-gray-500">
                                          <Car className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                          <p className="text-sm">No models found for this sub-brand</p>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                              </div>
                            )}

                            {/* Empty state for brand with no sub-brands */}
                            {expandedBrands.has(brand.id) && brand.subBrands.length === 0 && (
                              <div className="p-6 pl-20 text-center text-gray-500">
                                <Tag className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm">No sub-brands found for this brand</p>
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  )}

                  {/* Empty state for year with no brands */}
                  {expandedYears.has(year.id) && year.brands.length === 0 && (
                    <div className="p-6 text-center text-gray-500 border-t border-gray-200">
                      <Building2 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm">No brands found for this year</p>
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
      );
    },
  );
